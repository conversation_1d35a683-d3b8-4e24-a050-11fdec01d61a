import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../core/theme/app_theme.dart';
import '../../../shared/widgets/custom_text_field.dart';
import '../../../shared/widgets/custom_button.dart';
import '../../../shared/widgets/loading_overlay.dart';
import '../../../shared/models/patient_model.dart';
import '../bloc/patient_bloc.dart';
import '../bloc/patient_event.dart';
import '../bloc/patient_state.dart';

class AddPatientScreen extends StatefulWidget {
  const AddPatientScreen({super.key});

  @override
  State<AddPatientScreen> createState() => _AddPatientScreenState();
}

class _AddPatientScreenState extends State<AddPatientScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _pageController = PageController();
  late AnimationController _animationController;

  // Basic Information Controllers
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _middleNameController = TextEditingController();
  final _aadharController = TextEditingController();
  final _mobileController = TextEditingController();
  final _alternatePhoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _emergencyContactController = TextEditingController();
  final _emergencyContactNameController = TextEditingController();

  // Address Controllers
  final _addressController = TextEditingController();
  final _cityController = TextEditingController();
  final _stateController = TextEditingController();
  final _pincodeController = TextEditingController();
  final _countryController = TextEditingController();

  // Medical Information Controllers
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  final _bmiController = TextEditingController();
  final _allergiesController = TextEditingController();
  final _currentMedicationsController = TextEditingController();
  final _familyHistoryController = TextEditingController();
  final _socialHistoryController = TextEditingController();
  final _notesController = TextEditingController();

  // Form Data
  DateTime? _selectedDate;
  String? _selectedGender;
  String? _selectedBloodGroup;
  String? _selectedMaritalStatus;
  String? _selectedOccupation;
  String? _selectedEducation;
  String? _selectedReligion;
  String? _selectedNationality;
  int? _charlsonIndex;
  String? _selectedAsaGrade;
  String? _selectedEcogGrade;
  final List<String> _selectedComorbidities = [];
  final List<String> _selectedAllergies = [];
  final List<String> _selectedHabits = [];
  bool _isPregnant = false;
  bool _isBreastfeeding = false;
  bool _hasInsurance = false;
  String? _insuranceProvider;
  String? _policyNumber;

  int _currentPage = 0;
  final int _totalPages = 5;

  // Static Data Lists
  final List<String> _genderOptions = ['Male', 'Female', 'Other'];
  final List<String> _bloodGroupOptions = [
    'A+',
    'A-',
    'B+',
    'B-',
    'AB+',
    'AB-',
    'O+',
    'O-'
  ];
  final List<String> _maritalStatusOptions = [
    'Single',
    'Married',
    'Divorced',
    'Widowed',
    'Separated'
  ];
  final List<String> _educationOptions = [
    'No formal education',
    'Primary',
    'Secondary',
    'Higher Secondary',
    'Graduate',
    'Post Graduate',
    'Doctorate'
  ];
  final List<String> _occupationOptions = [
    'Student',
    'Homemaker',
    'Farmer',
    'Business',
    'Government Employee',
    'Private Employee',
    'Professional',
    'Retired',
    'Unemployed',
    'Other'
  ];
  final List<String> _religionOptions = [
    'Hindu',
    'Muslim',
    'Christian',
    'Sikh',
    'Buddhist',
    'Jain',
    'Other'
  ];
  final List<String> _nationalityOptions = ['Indian', 'Other'];

  final List<String> _asaGradeOptions = [
    'ASA I - Normal healthy patient',
    'ASA II - Mild systemic disease',
    'ASA III - Severe systemic disease',
    'ASA IV - Severe systemic disease that is a constant threat to life',
    'ASA V - Moribund patient who is not expected to survive without the operation',
    'ASA VI - Declared brain-dead patient whose organs are being removed for donor purposes'
  ];

  final List<String> _ecogGradeOptions = [
    'ECOG 0 - Fully active, able to carry on all pre-disease performance without restriction',
    'ECOG 1 - Restricted in physically strenuous activity but ambulatory and able to carry out work of a light or sedentary nature',
    'ECOG 2 - Ambulatory and capable of all selfcare but unable to carry out any work activities; up and about more than 50% of waking hours',
    'ECOG 3 - Capable of only limited selfcare; confined to bed or chair more than 50% of waking hours',
    'ECOG 4 - Completely disabled; cannot carry on any selfcare; totally confined to bed or chair',
    'ECOG 5 - Dead'
  ];

  final List<String> _comorbiditiesOptions = [
    'Diabetes Mellitus',
    'Hypertension',
    'Heart Disease',
    'Kidney Disease',
    'Liver Disease',
    'Lung Disease',
    'Cancer',
    'Stroke',
    'Arthritis',
    'Thyroid Disease',
    'Depression',
    'Anxiety',
    'Asthma',
    'COPD',
    'Epilepsy',
    'Migraine',
    'Osteoporosis',
    'Other'
  ];

  final List<String> _allergiesOptions = [
    'Drug Allergies',
    'Food Allergies',
    'Environmental Allergies',
    'Latex Allergy',
    'No Known Allergies'
  ];

  final List<String> _habitsOptions = [
    'Smoking',
    'Alcohol Consumption',
    'Tobacco Chewing',
    'Drug Use',
    'Regular Exercise',
    'Vegetarian Diet',
    'Non-Vegetarian Diet'
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();

    // Basic Information Controllers
    _firstNameController.dispose();
    _lastNameController.dispose();
    _middleNameController.dispose();
    _aadharController.dispose();
    _mobileController.dispose();
    _alternatePhoneController.dispose();
    _emailController.dispose();
    _emergencyContactController.dispose();
    _emergencyContactNameController.dispose();

    // Address Controllers
    _addressController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    _countryController.dispose();

    // Medical Information Controllers
    _heightController.dispose();
    _weightController.dispose();
    _bmiController.dispose();
    _allergiesController.dispose();
    _currentMedicationsController.dispose();
    _familyHistoryController.dispose();
    _socialHistoryController.dispose();
    _notesController.dispose();

    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      setState(() {
        _currentPage++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      setState(() {
        _currentPage--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _submitForm() {
    if (_formKey.currentState!.validate()) {
      if (_selectedDate == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select date of birth')),
        );
        return;
      }

      if (_selectedGender == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Please select gender')),
        );
        return;
      }

      // Calculate BMI if height and weight are provided
      double? calculatedBmi;
      final height = double.tryParse(_heightController.text);
      final weight = double.tryParse(_weightController.text);
      if (height != null && weight != null && height > 0) {
        final heightInMeters = height / 100;
        calculatedBmi = weight / (heightInMeters * heightInMeters);
      }

      final patient = PatientModel(
        id: '', // Will be generated by repository
        firstName: _firstNameController.text.trim(),
        lastName: _lastNameController.text.trim(),
        middleName: _middleNameController.text.trim().isEmpty
            ? null
            : _middleNameController.text.trim(),
        dateOfBirth: _selectedDate!,
        gender: _selectedGender!,
        aadharNumber: _aadharController.text.trim(),
        mobileNumber: _mobileController.text.trim(),
        alternatePhone: _alternatePhoneController.text.trim().isEmpty
            ? null
            : _alternatePhoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
        emergencyContact: _emergencyContactController.text.trim().isEmpty
            ? null
            : _emergencyContactController.text.trim(),
        emergencyContactName:
            _emergencyContactNameController.text.trim().isEmpty
                ? null
                : _emergencyContactNameController.text.trim(),
        address: _addressController.text.trim(),
        city: _cityController.text.trim().isEmpty
            ? null
            : _cityController.text.trim(),
        state: _stateController.text.trim().isEmpty
            ? null
            : _stateController.text.trim(),
        pincode: _pincodeController.text.trim().isEmpty
            ? null
            : _pincodeController.text.trim(),
        country: _countryController.text.trim().isEmpty
            ? 'India'
            : _countryController.text.trim(),
        maritalStatus: _selectedMaritalStatus,
        occupation: _selectedOccupation,
        education: _selectedEducation,
        religion: _selectedReligion,
        nationality: _selectedNationality ?? 'Indian',
        height: height,
        weight: weight,
        bmi: calculatedBmi,
        bloodGroup: _selectedBloodGroup,
        charlsonIndex: _charlsonIndex,
        asaGrade: _selectedAsaGrade,
        ecogGrade: _selectedEcogGrade,
        comorbidities: _selectedComorbidities,
        allergies: _selectedAllergies,
        habits: _selectedHabits,
        currentMedications: _currentMedicationsController.text.trim().isEmpty
            ? null
            : _currentMedicationsController.text.trim(),
        familyHistory: _familyHistoryController.text.trim().isEmpty
            ? null
            : _familyHistoryController.text.trim(),
        socialHistory: _socialHistoryController.text.trim().isEmpty
            ? null
            : _socialHistoryController.text.trim(),
        notes: _notesController.text.trim().isEmpty
            ? null
            : _notesController.text.trim(),
        isPregnant: _isPregnant,
        isBreastfeeding: _isBreastfeeding,
        hasInsurance: _hasInsurance,
        insuranceProvider: _insuranceProvider,
        policyNumber: _policyNumber,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: 'current-user', // Should be actual user ID
      );

      context.read<PatientBloc>().add(PatientCreateRequested(patient: patient));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Add New Patient'),
        elevation: 0,
        flexibleSpace: Container(
          decoration: const BoxDecoration(
            gradient: AppTheme.primaryGradient,
          ),
        ),
      ),
      body: BlocConsumer<PatientBloc, PatientState>(
        listener: (context, state) {
          if (state is PatientOperationSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppTheme.successColor,
                behavior: SnackBarBehavior.floating,
              ),
            );
            Navigator.pop(context);
          } else if (state is PatientError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppTheme.errorColor,
                behavior: SnackBarBehavior.floating,
              ),
            );
          }
        },
        builder: (context, state) {
          return LoadingOverlay(
            isLoading: state is PatientLoading,
            child: Column(
              children: [
                _buildProgressIndicator(),
                Expanded(
                  child: Form(
                    key: _formKey,
                    child: PageView(
                      controller: _pageController,
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _buildBasicInfoPage(),
                        _buildAddressInfoPage(),
                        _buildPersonalInfoPage(),
                        _buildMedicalInfoPage(),
                        _buildAdditionalInfoPage(),
                      ],
                    ),
                  ),
                ),
                _buildNavigationButtons(),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.primaryLightTeal,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: List.generate(_totalPages, (index) {
          final isActive = index <= _currentPage;
          final isCompleted = index < _currentPage;

          return Expanded(
            child: Container(
              margin: EdgeInsets.only(
                right: index < _totalPages - 1 ? 8 : 0,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      height: 4,
                      decoration: BoxDecoration(
                        color: isActive
                            ? AppTheme.primaryTeal
                            : AppTheme.mediumGrey,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  if (index < _totalPages - 1)
                    Container(
                      width: 20,
                      height: 20,
                      margin: const EdgeInsets.symmetric(horizontal: 4),
                      decoration: BoxDecoration(
                        color: isCompleted
                            ? AppTheme.primaryTeal
                            : isActive
                                ? AppTheme.primaryTeal
                                : AppTheme.mediumGrey,
                        shape: BoxShape.circle,
                      ),
                      child: isCompleted
                          ? const Icon(
                              Icons.check,
                              size: 12,
                              color: AppTheme.white,
                            )
                          : Text(
                              '${index + 1}',
                              style: const TextStyle(
                                color: AppTheme.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                            ),
                    ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildBasicInfoPage() {
    return AnimationLimiter(
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 600),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              Text(
                'Basic Information',
                style: AppTheme.headingMedium,
              ),
              const SizedBox(height: 24),

              // Name Fields
              Row(
                children: [
                  Expanded(
                    child: CustomTextField(
                      controller: _firstNameController,
                      label: 'First Name *',
                      hintText: 'Enter first name',
                      prefixIcon: Icons.person_outline,
                      textCapitalization: TextCapitalization.words,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter first name';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: CustomTextField(
                      controller: _middleNameController,
                      label: 'Middle Name',
                      hintText: 'Enter middle name',
                      prefixIcon: Icons.person_outline,
                      textCapitalization: TextCapitalization.words,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: CustomTextField(
                      controller: _lastNameController,
                      label: 'Last Name *',
                      hintText: 'Enter last name',
                      prefixIcon: Icons.person_outline,
                      textCapitalization: TextCapitalization.words,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter last name';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Date of Birth and Gender
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () async {
                        final date = await showDatePicker(
                          context: context,
                          initialDate: DateTime.now()
                              .subtract(const Duration(days: 365 * 30)),
                          firstDate: DateTime(1900),
                          lastDate: DateTime.now(),
                        );
                        if (date != null) {
                          setState(() {
                            _selectedDate = date;
                          });
                        }
                      },
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border.all(color: AppTheme.mediumGrey),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.calendar_today,
                                color: AppTheme.primaryTeal),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Date of Birth *',
                                    style: AppTheme.bodyMedium.copyWith(
                                      fontWeight: FontWeight.w600,
                                      color: AppTheme.darkGrey,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    _selectedDate != null
                                        ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                                        : 'Select date of birth',
                                    style: AppTheme.bodyMedium,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedGender,
                      decoration: InputDecoration(
                        labelText: 'Gender *',
                        prefixIcon: const Icon(Icons.person),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      items: _genderOptions.map((gender) {
                        return DropdownMenuItem(
                          value: gender,
                          child: Text(gender),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedGender = value;
                        });
                      },
                      validator: (value) {
                        if (value == null) {
                          return 'Please select gender';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Contact Information
              CustomTextField(
                controller: _aadharController,
                label: 'Aadhar Number *',
                hintText: 'Enter 12-digit Aadhar number',
                prefixIcon: Icons.credit_card,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter Aadhar number';
                  }
                  if (value.length != 12) {
                    return 'Aadhar number must be 12 digits';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              Row(
                children: [
                  Expanded(
                    child: CustomTextField(
                      controller: _mobileController,
                      label: 'Mobile Number *',
                      hintText: 'Enter 10-digit mobile number',
                      prefixIcon: Icons.phone,
                      keyboardType: TextInputType.phone,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter mobile number';
                        }
                        if (value.length != 10) {
                          return 'Mobile number must be 10 digits';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: CustomTextField(
                      controller: _alternatePhoneController,
                      label: 'Alternate Phone',
                      hintText: 'Enter alternate phone',
                      prefixIcon: Icons.phone_outlined,
                      keyboardType: TextInputType.phone,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              CustomTextField(
                controller: _emailController,
                label: 'Email Address',
                hintText: 'Enter email address (optional)',
                prefixIcon: Icons.email_outlined,
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                        .hasMatch(value)) {
                      return 'Please enter a valid email';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // Emergency Contact
              Text(
                'Emergency Contact',
                style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
              ),
              const SizedBox(height: 12),

              Row(
                children: [
                  Expanded(
                    child: CustomTextField(
                      controller: _emergencyContactNameController,
                      label: 'Emergency Contact Name',
                      hintText: 'Enter contact person name',
                      prefixIcon: Icons.person_pin,
                      textCapitalization: TextCapitalization.words,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: CustomTextField(
                      controller: _emergencyContactController,
                      label: 'Emergency Contact Number',
                      hintText: 'Enter emergency contact number',
                      prefixIcon: Icons.emergency,
                      keyboardType: TextInputType.phone,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddressInfoPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Address & Personal Information',
            style: AppTheme.headingMedium,
          ),
          const SizedBox(height: 24),

          // Address Information
          Text(
            'Address Information',
            style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          CustomTextField(
            controller: _addressController,
            label: 'Street Address *',
            hintText: 'Enter street address',
            prefixIcon: Icons.location_on_outlined,
            maxLines: 2,
            textCapitalization: TextCapitalization.words,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter address';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: _cityController,
                  label: 'City *',
                  hintText: 'Enter city',
                  prefixIcon: Icons.location_city,
                  textCapitalization: TextCapitalization.words,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter city';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CustomTextField(
                  controller: _stateController,
                  label: 'State *',
                  hintText: 'Enter state',
                  prefixIcon: Icons.map,
                  textCapitalization: TextCapitalization.words,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter state';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: _pincodeController,
                  label: 'Pincode *',
                  hintText: 'Enter pincode',
                  prefixIcon: Icons.pin_drop,
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter pincode';
                    }
                    if (value.length != 6) {
                      return 'Pincode must be 6 digits';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedNationality,
                  decoration: InputDecoration(
                    labelText: 'Country',
                    prefixIcon: const Icon(Icons.flag),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  items: _nationalityOptions.map((country) {
                    return DropdownMenuItem(
                      value: country,
                      child: Text(country),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedNationality = value;
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Personal Information
          Text(
            'Personal Information',
            style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedMaritalStatus,
                  decoration: InputDecoration(
                    labelText: 'Marital Status',
                    prefixIcon: const Icon(Icons.favorite),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  items: _maritalStatusOptions.map((status) {
                    return DropdownMenuItem(
                      value: status,
                      child: Text(status),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedMaritalStatus = value;
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedOccupation,
                  decoration: InputDecoration(
                    labelText: 'Occupation',
                    prefixIcon: const Icon(Icons.work),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  items: _occupationOptions.map((occupation) {
                    return DropdownMenuItem(
                      value: occupation,
                      child: Text(occupation),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedOccupation = value;
                    });
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedEducation,
                  decoration: InputDecoration(
                    labelText: 'Education',
                    prefixIcon: const Icon(Icons.school),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  items: _educationOptions.map((education) {
                    return DropdownMenuItem(
                      value: education,
                      child: Text(education),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedEducation = value;
                    });
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: DropdownButtonFormField<String>(
                  value: _selectedReligion,
                  decoration: InputDecoration(
                    labelText: 'Religion',
                    prefixIcon: const Icon(Icons.temple_hindu),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  items: _religionOptions.map((religion) {
                    return DropdownMenuItem(
                      value: religion,
                      child: Text(religion),
                    );
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedReligion = value;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Physical & Medical Information',
            style: AppTheme.headingMedium,
          ),
          const SizedBox(height: 24),

          // Physical Information
          Text(
            'Physical Information',
            style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Expanded(
                child: CustomTextField(
                  controller: _heightController,
                  label: 'Height (cm)',
                  hintText: 'Enter height',
                  prefixIcon: Icons.height,
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    // Auto-calculate BMI when height or weight changes
                    _calculateBMI();
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CustomTextField(
                  controller: _weightController,
                  label: 'Weight (kg)',
                  hintText: 'Enter weight',
                  prefixIcon: Icons.monitor_weight,
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    // Auto-calculate BMI when height or weight changes
                    _calculateBMI();
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: CustomTextField(
                  controller: _bmiController,
                  label: 'BMI',
                  hintText: 'Auto-calculated',
                  prefixIcon: Icons.calculate,
                  readOnly: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          DropdownButtonFormField<String>(
            value: _selectedBloodGroup,
            decoration: InputDecoration(
              labelText: 'Blood Group',
              prefixIcon: const Icon(Icons.bloodtype),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            items: _bloodGroupOptions.map((bloodGroup) {
              return DropdownMenuItem(
                value: bloodGroup,
                child: Text(bloodGroup),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedBloodGroup = value;
              });
            },
          ),
          const SizedBox(height: 24),

          // Special Conditions (for females)
          if (_selectedGender == 'Female') ...[
            Text(
              'Special Conditions',
              style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            CheckboxListTile(
              title: const Text('Currently Pregnant'),
              value: _isPregnant,
              onChanged: (value) {
                setState(() {
                  _isPregnant = value ?? false;
                });
              },
              activeColor: AppTheme.primaryTeal,
            ),
            CheckboxListTile(
              title: const Text('Currently Breastfeeding'),
              value: _isBreastfeeding,
              onChanged: (value) {
                setState(() {
                  _isBreastfeeding = value ?? false;
                });
              },
              activeColor: AppTheme.primaryTeal,
            ),
            const SizedBox(height: 16),
          ],

          // Habits
          Text(
            'Habits & Lifestyle',
            style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _habitsOptions.map((habit) {
              final isSelected = _selectedHabits.contains(habit);
              return FilterChip(
                label: Text(habit),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedHabits.add(habit);
                    } else {
                      _selectedHabits.remove(habit);
                    }
                  });
                },
                selectedColor: AppTheme.primaryTeal.withValues(alpha: 0.2),
                checkmarkColor: AppTheme.primaryTeal,
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildMedicalInfoPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Medical History & Assessment',
            style: AppTheme.headingMedium,
          ),
          const SizedBox(height: 24),

          // Comorbidities
          Text(
            'Comorbidities',
            style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _comorbiditiesOptions.map((comorbidity) {
              final isSelected = _selectedComorbidities.contains(comorbidity);
              return FilterChip(
                label: Text(comorbidity),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedComorbidities.add(comorbidity);
                    } else {
                      _selectedComorbidities.remove(comorbidity);
                    }
                  });
                },
                selectedColor: AppTheme.primaryTeal.withValues(alpha: 0.2),
                checkmarkColor: AppTheme.primaryTeal,
              );
            }).toList(),
          ),
          const SizedBox(height: 24),

          // Allergies
          Text(
            'Allergies',
            style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _allergiesOptions.map((allergy) {
              final isSelected = _selectedAllergies.contains(allergy);
              return FilterChip(
                label: Text(allergy),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedAllergies.add(allergy);
                    } else {
                      _selectedAllergies.remove(allergy);
                    }
                  });
                },
                selectedColor: AppTheme.errorColor.withValues(alpha: 0.2),
                checkmarkColor: AppTheme.errorColor,
              );
            }).toList(),
          ),
          const SizedBox(height: 24),

          // Medical Assessments
          Text(
            'Medical Assessments',
            style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 16),

          DropdownButtonFormField<String>(
            value: _selectedAsaGrade,
            decoration: InputDecoration(
              labelText: 'ASA Grade',
              prefixIcon: const Icon(Icons.medical_services),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            items: _asaGradeOptions.map((grade) {
              return DropdownMenuItem(
                value: grade,
                child: Text(grade, style: const TextStyle(fontSize: 12)),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedAsaGrade = value;
              });
            },
          ),
          const SizedBox(height: 16),

          DropdownButtonFormField<String>(
            value: _selectedEcogGrade,
            decoration: InputDecoration(
              labelText: 'ECOG Grade',
              prefixIcon: const Icon(Icons.accessibility),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            items: _ecogGradeOptions.map((grade) {
              return DropdownMenuItem(
                value: grade,
                child: Text(grade, style: const TextStyle(fontSize: 12)),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _selectedEcogGrade = value;
              });
            },
          ),
          const SizedBox(height: 16),

          // Charlson Index
          Text(
            'Charlson Comorbidity Index',
            style: AppTheme.bodyMedium.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8),

          Slider(
            value: (_charlsonIndex ?? 0).toDouble(),
            min: 0,
            max: 10,
            divisions: 10,
            label: _charlsonIndex?.toString() ?? '0',
            onChanged: (value) {
              setState(() {
                _charlsonIndex = value.round();
              });
            },
            activeColor: AppTheme.primaryTeal,
          ),
          Text(
            'Score: ${_charlsonIndex ?? 0}/10',
            style: AppTheme.bodySmall,
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfoPage() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Additional Information',
            style: AppTheme.headingMedium,
          ),
          const SizedBox(height: 24),

          // Current Medications
          CustomTextField(
            controller: _currentMedicationsController,
            label: 'Current Medications',
            hintText: 'List current medications (optional)',
            prefixIcon: Icons.medication,
            maxLines: 3,
          ),
          const SizedBox(height: 16),

          // Family History
          CustomTextField(
            controller: _familyHistoryController,
            label: 'Family History',
            hintText: 'Enter family medical history (optional)',
            prefixIcon: Icons.family_restroom,
            maxLines: 3,
          ),
          const SizedBox(height: 16),

          // Social History
          CustomTextField(
            controller: _socialHistoryController,
            label: 'Social History',
            hintText: 'Enter social history (optional)',
            prefixIcon: Icons.people,
            maxLines: 3,
          ),
          const SizedBox(height: 24),

          // Insurance Information
          Text(
            'Insurance Information',
            style: AppTheme.bodyLarge.copyWith(fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),

          CheckboxListTile(
            title: const Text('Has Health Insurance'),
            value: _hasInsurance,
            onChanged: (value) {
              setState(() {
                _hasInsurance = value ?? false;
                if (!_hasInsurance) {
                  _insuranceProvider = null;
                  _policyNumber = null;
                }
              });
            },
            activeColor: AppTheme.primaryTeal,
          ),

          if (_hasInsurance) ...[
            const SizedBox(height: 16),
            CustomTextField(
              controller: TextEditingController(text: _insuranceProvider),
              label: 'Insurance Provider',
              hintText: 'Enter insurance provider name',
              prefixIcon: Icons.business,
              onChanged: (value) {
                _insuranceProvider = value.isEmpty ? null : value;
              },
            ),
            const SizedBox(height: 16),
            CustomTextField(
              controller: TextEditingController(text: _policyNumber),
              label: 'Policy Number',
              hintText: 'Enter policy number',
              prefixIcon: Icons.confirmation_number,
              onChanged: (value) {
                _policyNumber = value.isEmpty ? null : value;
              },
            ),
          ],
          const SizedBox(height: 24),

          // Additional Notes
          CustomTextField(
            controller: _notesController,
            label: 'Additional Notes',
            hintText: 'Enter any additional notes (optional)',
            prefixIcon: Icons.note_add,
            maxLines: 4,
          ),
        ],
      ),
    );
  }

  void _calculateBMI() {
    final height = double.tryParse(_heightController.text);
    final weight = double.tryParse(_weightController.text);

    if (height != null && weight != null && height > 0) {
      final heightInMeters = height / 100;
      final bmi = weight / (heightInMeters * heightInMeters);
      _bmiController.text = bmi.toStringAsFixed(1);
    } else {
      _bmiController.text = '';
    }
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentPage > 0)
            Expanded(
              child: CustomButton(
                text: 'Previous',
                type: ButtonType.outline,
                onPressed: _previousPage,
              ),
            ),
          if (_currentPage > 0) const SizedBox(width: 16),
          Expanded(
            child: CustomButton(
              text: _currentPage == _totalPages - 1 ? 'Save Patient' : 'Next',
              onPressed:
                  _currentPage == _totalPages - 1 ? _submitForm : _nextPage,
            ),
          ),
        ],
      ),
    );
  }
}
