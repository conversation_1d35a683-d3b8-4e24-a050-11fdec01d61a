<lint-module
    format="1"
    dir="E:\Ongoing\project_v\project_v\android\app"
    name=":app"
    type="APP"
    maven="android:app:"
    agpVersion="8.1.0"
    buildFolder="E:\Ongoing\project_v\project_v\build\app"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\33.0.1\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-35">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
