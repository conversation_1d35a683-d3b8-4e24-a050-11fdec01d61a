import 'package:equatable/equatable.dart';

class PatientModel extends Equatable {
  // Basic Information
  final String id;
  final String firstName;
  final String lastName;
  final String? middleName;
  final DateTime dateOfBirth;
  final String gender;
  final String aadharNumber;
  final String mobileNumber;
  final String? alternatePhone;
  final String? email;
  final String? emergencyContact;
  final String? emergencyContactName;

  // Address Information
  final String address;
  final String? city;
  final String? state;
  final String? pincode;
  final String? country;

  // Personal Information
  final String? maritalStatus;
  final String? occupation;
  final String? education;
  final String? religion;
  final String? nationality;

  // Medical Information
  final double? height; // in cm
  final double? weight; // in kg
  final double? bmi;
  final String? bloodGroup;
  final int? charlsonIndex;
  final String? asaGrade;
  final String? ecogGrade;
  final String? profileImageUrl;
  final List<String> comorbidities;
  final List<String> allergies;
  final List<String> habits;
  final String? currentMedications;
  final String? familyHistory;
  final String? socialHistory;
  final String? notes;

  // Special Conditions
  final bool isPregnant;
  final bool isBreastfeeding;

  // Insurance Information
  final bool hasInsurance;
  final String? insuranceProvider;
  final String? policyNumber;

  // System Information
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;

  const PatientModel({
    required this.id,
    required this.firstName,
    required this.lastName,
    this.middleName,
    required this.dateOfBirth,
    required this.gender,
    required this.aadharNumber,
    required this.mobileNumber,
    this.alternatePhone,
    this.email,
    this.emergencyContact,
    this.emergencyContactName,
    required this.address,
    this.city,
    this.state,
    this.pincode,
    this.country,
    this.maritalStatus,
    this.occupation,
    this.education,
    this.religion,
    this.nationality,
    this.height,
    this.weight,
    this.bmi,
    this.bloodGroup,
    this.charlsonIndex,
    this.asaGrade,
    this.ecogGrade,
    this.profileImageUrl,
    this.comorbidities = const [],
    this.allergies = const [],
    this.habits = const [],
    this.currentMedications,
    this.familyHistory,
    this.socialHistory,
    this.notes,
    this.isPregnant = false,
    this.isBreastfeeding = false,
    this.hasInsurance = false,
    this.insuranceProvider,
    this.policyNumber,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
  });

  String get fullName => '$firstName $lastName';

  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month ||
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  double? get calculatedBmi {
    if (height != null && weight != null && height! > 0) {
      final heightInMeters = height! / 100;
      return weight! / (heightInMeters * heightInMeters);
    }
    return bmi;
  }

  String get bmiCategory {
    final bmiValue = calculatedBmi;
    if (bmiValue == null) return 'Unknown';

    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal';
    if (bmiValue < 30) return 'Overweight';
    return 'Obese';
  }

  factory PatientModel.fromJson(Map<String, dynamic> json) {
    return PatientModel(
      id: json['id'] as String,
      firstName: json['first_name'] as String,
      lastName: json['last_name'] as String,
      middleName: json['middle_name'] as String?,
      dateOfBirth: DateTime.parse(json['date_of_birth'] as String),
      gender: json['gender'] as String,
      aadharNumber: json['aadhar_number'] as String,
      mobileNumber: json['mobile_number'] as String,
      alternatePhone: json['alternate_phone'] as String?,
      email: json['email'] as String?,
      emergencyContact: json['emergency_contact'] as String?,
      emergencyContactName: json['emergency_contact_name'] as String?,
      address: json['address'] as String,
      city: json['city'] as String?,
      state: json['state'] as String?,
      pincode: json['pincode'] as String?,
      country: json['country'] as String?,
      maritalStatus: json['marital_status'] as String?,
      occupation: json['occupation'] as String?,
      education: json['education'] as String?,
      religion: json['religion'] as String?,
      nationality: json['nationality'] as String?,
      height: (json['height'] as num?)?.toDouble(),
      weight: (json['weight'] as num?)?.toDouble(),
      bmi: (json['bmi'] as num?)?.toDouble(),
      bloodGroup: json['blood_group'] as String?,
      charlsonIndex: json['charlson_index'] as int?,
      asaGrade: json['asa_grade'] as String?,
      ecogGrade: json['ecog_grade'] as String?,
      profileImageUrl: json['profile_image_url'] as String?,
      comorbidities: (json['comorbidities'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      allergies: (json['allergies'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      habits: (json['habits'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      currentMedications: json['current_medications'] as String?,
      familyHistory: json['family_history'] as String?,
      socialHistory: json['social_history'] as String?,
      notes: json['notes'] as String?,
      isPregnant: json['is_pregnant'] as bool? ?? false,
      isBreastfeeding: json['is_breastfeeding'] as bool? ?? false,
      hasInsurance: json['has_insurance'] as bool? ?? false,
      insuranceProvider: json['insurance_provider'] as String?,
      policyNumber: json['policy_number'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      createdBy: json['created_by'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'first_name': firstName,
      'last_name': lastName,
      'middle_name': middleName,
      'date_of_birth': dateOfBirth.toIso8601String(),
      'gender': gender,
      'aadhar_number': aadharNumber,
      'mobile_number': mobileNumber,
      'alternate_phone': alternatePhone,
      'email': email,
      'emergency_contact': emergencyContact,
      'emergency_contact_name': emergencyContactName,
      'address': address,
      'city': city,
      'state': state,
      'pincode': pincode,
      'country': country,
      'marital_status': maritalStatus,
      'occupation': occupation,
      'education': education,
      'religion': religion,
      'nationality': nationality,
      'height': height,
      'weight': weight,
      'bmi': bmi,
      'blood_group': bloodGroup,
      'charlson_index': charlsonIndex,
      'asa_grade': asaGrade,
      'ecog_grade': ecogGrade,
      'profile_image_url': profileImageUrl,
      'comorbidities': comorbidities,
      'allergies': allergies,
      'habits': habits,
      'current_medications': currentMedications,
      'family_history': familyHistory,
      'social_history': socialHistory,
      'notes': notes,
      'is_pregnant': isPregnant,
      'is_breastfeeding': isBreastfeeding,
      'has_insurance': hasInsurance,
      'insurance_provider': insuranceProvider,
      'policy_number': policyNumber,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'created_by': createdBy,
    };
  }

  PatientModel copyWith({
    String? id,
    String? firstName,
    String? lastName,
    String? middleName,
    DateTime? dateOfBirth,
    String? gender,
    String? aadharNumber,
    String? mobileNumber,
    String? alternatePhone,
    String? email,
    String? emergencyContact,
    String? emergencyContactName,
    String? address,
    String? city,
    String? state,
    String? pincode,
    String? country,
    String? maritalStatus,
    String? occupation,
    String? education,
    String? religion,
    String? nationality,
    double? height,
    double? weight,
    double? bmi,
    String? bloodGroup,
    int? charlsonIndex,
    String? asaGrade,
    String? ecogGrade,
    String? profileImageUrl,
    List<String>? comorbidities,
    List<String>? allergies,
    List<String>? habits,
    String? currentMedications,
    String? familyHistory,
    String? socialHistory,
    String? notes,
    bool? isPregnant,
    bool? isBreastfeeding,
    bool? hasInsurance,
    String? insuranceProvider,
    String? policyNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return PatientModel(
      id: id ?? this.id,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      middleName: middleName ?? this.middleName,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      gender: gender ?? this.gender,
      aadharNumber: aadharNumber ?? this.aadharNumber,
      mobileNumber: mobileNumber ?? this.mobileNumber,
      alternatePhone: alternatePhone ?? this.alternatePhone,
      email: email ?? this.email,
      emergencyContact: emergencyContact ?? this.emergencyContact,
      emergencyContactName: emergencyContactName ?? this.emergencyContactName,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      pincode: pincode ?? this.pincode,
      country: country ?? this.country,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      occupation: occupation ?? this.occupation,
      education: education ?? this.education,
      religion: religion ?? this.religion,
      nationality: nationality ?? this.nationality,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      bmi: bmi ?? this.bmi,
      bloodGroup: bloodGroup ?? this.bloodGroup,
      charlsonIndex: charlsonIndex ?? this.charlsonIndex,
      asaGrade: asaGrade ?? this.asaGrade,
      ecogGrade: ecogGrade ?? this.ecogGrade,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      comorbidities: comorbidities ?? this.comorbidities,
      allergies: allergies ?? this.allergies,
      habits: habits ?? this.habits,
      currentMedications: currentMedications ?? this.currentMedications,
      familyHistory: familyHistory ?? this.familyHistory,
      socialHistory: socialHistory ?? this.socialHistory,
      notes: notes ?? this.notes,
      isPregnant: isPregnant ?? this.isPregnant,
      isBreastfeeding: isBreastfeeding ?? this.isBreastfeeding,
      hasInsurance: hasInsurance ?? this.hasInsurance,
      insuranceProvider: insuranceProvider ?? this.insuranceProvider,
      policyNumber: policyNumber ?? this.policyNumber,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  @override
  List<Object?> get props => [
        id,
        firstName,
        lastName,
        middleName,
        dateOfBirth,
        gender,
        aadharNumber,
        mobileNumber,
        alternatePhone,
        email,
        emergencyContact,
        emergencyContactName,
        address,
        city,
        state,
        pincode,
        country,
        maritalStatus,
        occupation,
        education,
        religion,
        nationality,
        height,
        weight,
        bmi,
        bloodGroup,
        charlsonIndex,
        asaGrade,
        ecogGrade,
        profileImageUrl,
        comorbidities,
        allergies,
        habits,
        currentMedications,
        familyHistory,
        socialHistory,
        notes,
        isPregnant,
        isBreastfeeding,
        hasInsurance,
        insuranceProvider,
        policyNumber,
        createdAt,
        updatedAt,
        createdBy,
      ];
}

class PatientSearchResult {
  final List<PatientModel> patients;
  final int totalCount;
  final int currentPage;
  final int totalPages;

  const PatientSearchResult({
    required this.patients,
    required this.totalCount,
    required this.currentPage,
    required this.totalPages,
  });

  factory PatientSearchResult.fromJson(Map<String, dynamic> json) {
    return PatientSearchResult(
      patients: (json['patients'] as List<dynamic>)
          .map((e) => PatientModel.fromJson(e as Map<String, dynamic>))
          .toList(),
      totalCount: json['total_count'] as int,
      currentPage: json['current_page'] as int,
      totalPages: json['total_pages'] as int,
    );
  }
}
