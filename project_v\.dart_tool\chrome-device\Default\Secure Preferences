{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.158\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.158\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.158\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.158\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"account_values": {"browser": {"show_home_button": "CED0351E2FD397B3741C2BFC2FA04536549788CA1161930851FA8E155A5B97DC"}, "extensions": {"ui": {"developer_mode": "D98F3205622DFA21E5D567294C5A2D137CC09156CD75996128BB7A0847D7F6F7"}}, "homepage": "66F41AAE7D8C3235347FEC1728271C4613C19B978D94478516EF1F6EA4073885", "homepage_is_newtabpage": "250F3B5B8629AA31C904E498E27810CEC2E07682932D9A5C1F0EA677ECA68A43", "session": {"restore_on_startup": "47F6D3596621CEF91080EE0966FE7908D29E12B38352B865CB105BD4A0325813", "startup_urls": "210F8DBCC4D3A582AB9A2212F3048F2FF58E33A1E91A144440AFEABCFAF18D0D"}}, "browser": {"show_home_button": "6B210AF3EACB2A82C75E0C5F626ABEEAE59ABDFA29808C85170DF1ADB10824EF"}, "default_search_provider_data": {"template_url_data": "DED8A8C31EEB5B4C24A9FB2D4BF50CA510534081DE5A08D0DBDB187D7938DA2D"}, "enterprise_signin": {"policy_recovery_token": "69B0CDC04B8084D2AE5B7348582E8F63359E3FD8ECFB8B9E44B655A0A86E59AC"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "98A7DB932B3E9CC2E98FFCBD520CA930104D063525982F46A139D98D72FB413A", "fignfifoniblkonapihmkfakmlgkbkcf": "CEC6C8494158FDE79DD908D68372B174C530BB2450D2C6942E7DEC442E5CF532", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "562C042B414ECC0071EF25B81CFA83771B78FC345DEDF363DB5081ECD6D853B8", "nkeimhogjdpnpccoofpliimaahmaaome": "400EB4BFF41722704372C753C147FBFFA71FDAE8C1B0A6D17171157D85D62201"}, "ui": {"developer_mode": "86A8D5E63970B924F9AAC5A394D31AB2C9D35F0DCFD0748A4053DE3021E26899"}}, "google": {"services": {"account_id": "B1C02C007984CA902F050E1F76B67B2C016BDC11B920D5ED30BAFDCE3D5F0602", "last_signed_in_username": "2765E01EE8E76518BF0B35C425DAE0BAD45E182FE37562C8B9951395420477CE", "last_username": "8AA3B8CA0CB9BDD7556DE52CC6A2B602027229F61393A96768B1CD1F2A7B2E9F"}}, "homepage": "D8A446E180DB67A6776CEDE74FFC2FD4E77CFDA106E54E42AD12A827D35568E0", "homepage_is_newtabpage": "409A3517A0680174A8EFA30148FFEC36529CDC4967C76784475FBF40C062352D", "media": {"cdm": {"origin_data": "0EBCE2C49A59090F47812B7C3F7144BFDEC0D4AFFBAE8016E482332CAF638904"}, "storage_id_salt": "02116F229C55FB890A88034A5D08F0336FF1A06224DADF17ECF44C3ED04015FB"}, "module_blocklist_cache_md5_digest": "D44497165E85CD9DCB917DF92B4FB2DEE394E99AAE7339FF94602EFDA95FB482", "pinned_tabs": "DE6A9C667D81FA11F82670A5E5E5808808BF1BA93856173F7AF516C8DB2D6BDF", "prefs": {"preference_reset_time": "A4FEB8BE0F1343AB4146E0C41F87208A89E443D03FC13AA14EDF55F7183399C8"}, "safebrowsing": {"incidents_sent": "D8787D9C1308F5713E2F22BD952E04F729D1D50FC6A86C5EFD0E05F930A7DEAD"}, "search_provider_overrides": "3D926678FCC640CB55B84825655413CC90EA6D0345FB1A7CFF685CE5814DE7C4", "session": {"restore_on_startup": "B519E3D149AEAE69EE503C9348CE1181DD70D96EF48DCB64A0E52AFB4F280EEB", "startup_urls": "141F004B43D18A41E01615559126B20599D7F3BA87CB5FAC2F2D6AADE4442A9E"}}, "super_mac": "91B528EC5CE991F42AAD092A31E8BAE605D55C2A45597A94AF2B15C77DADE052"}, "safebrowsing": {"incidents_sent": {"1": {"account_values.extensions.ui.developer_mode": "**********", "account_values.homepage": "*********", "account_values.session.startup_urls": "**********", "extensions.settings": "********", "extensions.ui.developer_mode": "**********", "google.services.account_id": "*********", "google.services.last_signed_in_username": "**********", "google.services.last_username": "**********", "homepage": "**********", "media.cdm.origin_data": "**********", "pinned_tabs": "**********", "safebrowsing.incidents_sent": "**********", "session.startup_urls": "**********"}}}}