<variant
    name="release"
    package="com.medical.projectv.project_v"
    minSdkVersion="21"
    targetSdkVersion="35"
    shrinking="true"
    mergedManifest="E:\Ongoing\project_v\project_v\build\app\intermediates\merged_manifest\release\AndroidManifest.xml"
    manifestMergeReport="E:\Ongoing\project_v\project_v\build\app\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="E:\Ongoing\project_v\project_v\build\app\intermediates\default_proguard_files\global\proguard-android-optimize.txt-8.1.0;C:\Users\<USER>\dev\flutter\packages\flutter_tools\gradle\flutter_proguard_rules.pro;proguard-rules.pro"
    partialResultsDir="E:\Ongoing\project_v\project_v\build\app\intermediates\lint_vital_partial_results\release\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <manifestPlaceholders>
    <placeholder
        name="applicationName"
        value="android.app.Application" />
  </manifestPlaceholders>
  <mainArtifact
      classOutputs="E:\Ongoing\project_v\project_v\build\app\intermediates\javac\release\classes;E:\Ongoing\project_v\project_v\build\app\tmp\kotlin-classes\release;E:\Ongoing\project_v\project_v\build\app\kotlinToolingMetadata;E:\Ongoing\project_v\project_v\build\app\intermediates\compile_and_runtime_not_namespaced_r_class_jar\release\R.jar"
      applicationId="com.medical.projectv.project_v"
      generatedSourceFolders="E:\Ongoing\project_v\project_v\build\app\generated\ap_generated_sources\release\out"
      generatedResourceFolders="E:\Ongoing\project_v\project_v\build\app\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\transforms-3\922f7fa658e8c6bd45dbddcedbc8388c\transformed\D8BackportedDesugaredMethods.txt">
  </mainArtifact>
</variant>
